"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination"
import {
  Search,
  Filter,
  ShoppingBag,
  Calendar,
  Clock,
  User,
  MapPin,
  Package,
  Truck,
  Eye,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Save,
  Trash2,
  Loader2,
  RefreshCw
} from "lucide-react"
import { useAdminOrders, useAdminOrderDetails, useUpdateOrderStatus, useOrderStats } from "@/hooks/use-admin-orders"
import { type AdminOrder, type UpdateOrderStatusData } from "@/lib/api"
import { toast } from "sonner"

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [paymentFilter, setPaymentFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState<string>("")
  const [editedOrder, setEditedOrder] = useState<UpdateOrderStatusData>({})

  // API hooks
  const {
    data: ordersData,
    isLoading: ordersLoading,
    error: ordersError,
    refetch: refetchOrders
  } = useAdminOrders({
    page: currentPage,
    limit: 10,
    status: statusFilter !== 'all' ? statusFilter : undefined
  })

  const {
    data: orderDetailsData,
    isLoading: detailsLoading
  } = useAdminOrderDetails(selectedOrderId)

  const updateOrderStatusMutation = useUpdateOrderStatus()
  const orderStats = useOrderStats()

  const orders = ordersData?.orders || []
  const pagination = ordersData?.pagination

  // Lọc đơn hàng theo search term và payment status
  const filteredOrders = useMemo(() => {
    return orders.filter((order) => {
      const customerName = `${order.user_id.first_name || ''} ${order.user_id.last_name || ''}`.trim()
      const matchesSearch =
        order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.user_id.email.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesPayment = paymentFilter === "all" || order.payment_status === paymentFilter

      return matchesSearch && matchesPayment
    })
  }, [orders, searchTerm, paymentFilter])
  // Định dạng số tiền
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price)
  }

  // Định dạng ngày tháng
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Xử lý mở dialog xem chi tiết đơn hàng
  const handleViewOrder = (orderId: string) => {
    setSelectedOrderId(orderId)
    setIsViewDialogOpen(true)
  }

  // Xử lý mở dialog chỉnh sửa đơn hàng
  const handleEditOrder = (order: AdminOrder) => {
    setSelectedOrderId(order._id)
    setEditedOrder({
      order_status: order.order_status,
      payment_status: order.payment_status
    })
    setIsEditDialogOpen(true)
  }

  // Xử lý lưu thay đổi đơn hàng
  const handleSaveOrder = async () => {
    if (!selectedOrderId) return

    try {
      await updateOrderStatusMutation.mutateAsync({
        orderId: selectedOrderId,
        data: editedOrder
      })
      setIsEditDialogOpen(false)
      setSelectedOrderId("")
      setEditedOrder({})
    } catch (error) {
      // Error được xử lý trong hook
    }
  }

  // Xử lý thay đổi trạng thái đơn hàng
  const handleStatusChange = (value: string) => {
    setEditedOrder(prev => ({...prev, order_status: value as any}))
  }

  // Xử lý thay đổi trạng thái thanh toán
  const handlePaymentStatusChange = (value: string) => {
    setEditedOrder(prev => ({...prev, payment_status: value as any}))
  }

  // Xử lý refresh data
  const handleRefresh = () => {
    refetchOrders()
    toast.success('Đã làm mới dữ liệu')
  }
  
  // Xác định màu sắc cho badge trạng thái đơn hàng
  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-500 hover:bg-green-600"
      case "shipping":
        return "bg-blue-500 hover:bg-blue-600"
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "confirmed":
        return "bg-purple-500 hover:bg-purple-600"
      case "cancelled":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  // Xác định màu sắc cho badge trạng thái thanh toán
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-500 hover:bg-green-600"
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "failed":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  // Xác định icon cho trạng thái đơn hàng
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "delivered":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "shipping":
        return <Truck className="h-5 w-5 text-blue-500" />
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-500" />
      case "confirmed":
        return <FileText className="h-5 w-5 text-purple-500" />
      case "cancelled":
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  // Chuyển đổi status sang tiếng Việt
  const getStatusText = (status: string) => {
    switch (status) {
      case "pending": return "Đang xử lý"
      case "confirmed": return "Đã xác nhận"
      case "shipping": return "Đang giao hàng"
      case "delivered": return "Đã giao hàng"
      case "cancelled": return "Đã hủy"
      default: return status
    }
  }

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case "pending": return "Chưa thanh toán"
      case "paid": return "Đã thanh toán"
      case "failed": return "Thanh toán thất bại"
      default: return status
    }
  }

  // Loading state
  if (ordersLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <ShoppingBag className="h-6 w-6 mr-2 text-orange-500" />
            <h1 className="text-3xl font-bold">Quản lý đơn hàng</h1>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="space-y-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Skeleton className="h-10 bg-gray-700" />
                <Skeleton className="h-10 bg-gray-700" />
                <Skeleton className="h-10 bg-gray-700" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800 border-gray-700">
            <CardContent className="p-0">
              <div className="space-y-2 p-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton key={i} className="h-16 bg-gray-700" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Error state
  if (ordersError) {
    return (
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <ShoppingBag className="h-6 w-6 mr-2 text-orange-500" />
            <h1 className="text-3xl font-bold">Quản lý đơn hàng</h1>
          </div>
        </div>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-6 text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Có lỗi xảy ra</h3>
            <p className="text-gray-400 mb-4">Không thể tải danh sách đơn hàng</p>
            <Button onClick={handleRefresh} className="bg-orange-600 hover:bg-orange-700">
              <RefreshCw className="h-4 w-4 mr-2" />
              Thử lại
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <ShoppingBag className="h-6 w-6 mr-2 text-orange-500" />
          <h1 className="text-3xl font-bold">Quản lý đơn hàng</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Tổng đơn hàng</p>
                <p className="text-2xl font-bold text-white">{orderStats.total}</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Đang xử lý</p>
                <p className="text-2xl font-bold text-yellow-500">{orderStats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div>
              <p className="text-gray-400 text-sm">Đã giao hàng</p>
              <p className="text-2xl font-bold text-green-500">{orderStats.delivered}</p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-4">
            <div>
              <p className="text-gray-400 text-sm">Doanh thu</p>
              <p className="text-2xl font-bold text-orange-500">{formatPrice(orderStats.totalRevenue)}</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card className="bg-gray-800 border-gray-700 mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-lg">Bộ lọc</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Tìm theo mã đơn, tên khách hàng..."
                className="bg-gray-700 border-gray-600 text-white pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Trạng thái đơn hàng" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600 text-white">
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="pending">Đang xử lý</SelectItem>
                <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                <SelectItem value="shipping">Đang giao hàng</SelectItem>
                <SelectItem value="delivered">Đã giao hàng</SelectItem>
                <SelectItem value="cancelled">Đã hủy</SelectItem>
              </SelectContent>
            </Select>

            <Select value={paymentFilter} onValueChange={setPaymentFilter}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Trạng thái thanh toán" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600 text-white">
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="paid">Đã thanh toán</SelectItem>
                <SelectItem value="pending">Chưa thanh toán</SelectItem>
                <SelectItem value="failed">Thanh toán thất bại</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-0">
          <Table>
            <TableHeader className="bg-gray-900">
              <TableRow className="border-gray-700 hover:bg-gray-900">
                <TableHead className="text-gray-400">Mã ĐH</TableHead>
                <TableHead className="text-gray-400">Khách hàng</TableHead>
                <TableHead className="text-gray-400">Ngày đặt</TableHead>
                <TableHead className="text-gray-400">Tổng tiền</TableHead>
                <TableHead className="text-gray-400">Trạng thái</TableHead>
                <TableHead className="text-gray-400">Thanh toán</TableHead>
                <TableHead className="text-gray-400 text-right">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => {
                const customerName = `${order.user_id.first_name || ''} ${order.user_id.last_name || ''}`.trim() || 'N/A'

                return (
                  <TableRow key={order._id} className="border-gray-700 hover:bg-gray-700">
                    <TableCell className="font-medium text-white">{order.order_number}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium text-white">{customerName}</div>
                        <div className="text-sm text-gray-400">{order.user_id.email}</div>
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-300">{formatDate(order.created_at)}</TableCell>
                    <TableCell className="text-orange-500 font-medium">{formatPrice(order.total_amount)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(order.order_status)}>
                        {getStatusText(order.order_status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getPaymentStatusColor(order.payment_status)}>
                        {getPaymentStatusText(order.payment_status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 border-gray-600 text-gray-300 hover:text-white hover:bg-gray-600"
                          onClick={() => handleViewOrder(order._id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 border-gray-600 text-blue-400 hover:text-white hover:bg-blue-900 hover:border-blue-700"
                          onClick={() => handleEditOrder(order)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}

              {filteredOrders.length === 0 && !ordersLoading && (
                <TableRow className="border-gray-700">
                  <TableCell colSpan={7} className="h-24 text-center text-gray-400">
                    {searchTerm || paymentFilter !== 'all' ? 'Không tìm thấy đơn hàng nào phù hợp' : 'Chưa có đơn hàng nào'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      {/* Pagination */}
      {pagination && pagination.pages > 1 && (
        <div className="mt-4 flex justify-between items-center text-gray-400">
          <div>
            Hiển thị {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} trong tổng số {pagination.total} đơn hàng
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                />
              </PaginationItem>

              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                const page = i + 1
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              })}

              {pagination.pages > 5 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setCurrentPage(Math.min(pagination.pages, currentPage + 1))}
                  className={currentPage === pagination.pages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
      
      {/* Dialog xem chi tiết đơn hàng */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-xl text-white flex items-center">
              {orderDetailsData?.order && getStatusIcon(orderDetailsData.order.order_status)}
              <span className="ml-2">Chi tiết đơn hàng {orderDetailsData?.order?.order_number}</span>
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              Thông tin chi tiết về đơn hàng và trạng thái
            </DialogDescription>
          </DialogHeader>

          {detailsLoading ? (
            <div className="py-8 flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
            </div>
          ) : orderDetailsData?.order ? (
            <div className="py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Thông tin đơn hàng */}
                <div className="space-y-4">
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-2 flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      Thông tin đơn hàng
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Mã đơn hàng:</span>
                        <span className="text-white font-medium">{orderDetailsData.order.order_number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Ngày đặt:</span>
                        <span className="text-white">{formatDate(orderDetailsData.order.created_at)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Trạng thái:</span>
                        <Badge className={getStatusColor(orderDetailsData.order.order_status)}>
                          {getStatusText(orderDetailsData.order.order_status)}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Phương thức thanh toán:</span>
                        <span className="text-white">{orderDetailsData.order.payment_method}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Trạng thái thanh toán:</span>
                        <Badge className={getPaymentStatusColor(orderDetailsData.order.payment_status)}>
                          {getPaymentStatusText(orderDetailsData.order.payment_status)}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Phí vận chuyển:</span>
                        <span className="text-white">{formatPrice(orderDetailsData.order.shipping_fee)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-2 flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-400" />
                      Thông tin khách hàng
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Tên khách hàng:</span>
                        <span className="text-white">{orderDetailsData.order.shipping_recipient_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Email:</span>
                        <span className="text-white">{orderDetailsData.order.user_id.email}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-2 flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                      Địa chỉ giao hàng
                    </h3>
                    <p className="text-sm text-white">{orderDetailsData.order.shipping_address}</p>
                    {orderDetailsData.order.notes && (
                      <div className="mt-2 pt-2 border-t border-gray-600">
                        <p className="text-gray-400 text-xs">Ghi chú:</p>
                        <p className="text-sm text-white">{orderDetailsData.order.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Chi tiết sản phẩm */}
                <div className="bg-gray-700/50 p-4 rounded-lg">
                  <h3 className="text-white font-medium mb-4 flex items-center">
                    <Package className="h-4 w-4 mr-2 text-gray-400" />
                    Chi tiết sản phẩm
                  </h3>
                  <div className="space-y-3">
                    {orderDetailsData.orderItems?.map((item: any, index: number) => (
                      <div key={index} className="flex justify-between border-b border-gray-600 pb-2 last:border-0">
                        <div>
                          <div className="text-white">{item.product_name}</div>
                          <div className="text-sm text-gray-400">
                            SL: {item.quantity} × {formatPrice(item.unit_price)}
                          </div>
                        </div>
                        <div className="text-right text-orange-500 font-medium">
                          {formatPrice(item.total_price)}
                        </div>
                      </div>
                    ))}

                    <div className="pt-2 mt-2 border-t border-gray-600">
                      <div className="flex justify-between text-gray-400">
                        <span>Tạm tính:</span>
                        <span>{formatPrice(orderDetailsData.order.subtotal)}</span>
                      </div>
                      <div className="flex justify-between text-gray-400">
                        <span>Phí vận chuyển:</span>
                        <span>{formatPrice(orderDetailsData.order.shipping_fee)}</span>
                      </div>
                      <div className="flex justify-between font-medium text-white mt-2">
                        <span>Tổng tiền:</span>
                        <span className="text-orange-500">{formatPrice(orderDetailsData.order.total_amount)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-between">
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    In hóa đơn
                  </Button>
                </div>
                <Button 
                  onClick={() => setIsViewDialogOpen(false)}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Đóng
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Dialog chỉnh sửa đơn hàng */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-gray-800 text-white border-gray-700 sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="text-xl text-white flex items-center">
              <Edit className="h-5 w-5 mr-2 text-blue-500" />
              <span>Chỉnh sửa đơn hàng {orderDetailsData?.order?.order_number}</span>
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              Cập nhật trạng thái đơn hàng và thanh toán
            </DialogDescription>
          </DialogHeader>

          {orderDetailsData?.order && (
            <div className="py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Thông tin đơn hàng */}
                <div className="space-y-4">
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      Thông tin đơn hàng
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="orderStatus" className="text-gray-300 mb-1 block">
                          Trạng thái đơn hàng
                        </Label>
                        <Select value={editedOrder.order_status || orderDetailsData.order.order_status} onValueChange={handleStatusChange}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Chọn trạng thái" />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600 text-white">
                            <SelectItem value="pending">Đang xử lý</SelectItem>
                            <SelectItem value="confirmed">Đã xác nhận</SelectItem>
                            <SelectItem value="shipping">Đang giao hàng</SelectItem>
                            <SelectItem value="delivered">Đã giao hàng</SelectItem>
                            <SelectItem value="cancelled">Đã hủy</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="paymentStatus" className="text-gray-300 mb-1 block">
                          Trạng thái thanh toán
                        </Label>
                        <Select value={editedOrder.payment_status || orderDetailsData.order.payment_status} onValueChange={handlePaymentStatusChange}>
                          <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                            <SelectValue placeholder="Chọn trạng thái" />
                          </SelectTrigger>
                          <SelectContent className="bg-gray-700 border-gray-600 text-white">
                            <SelectItem value="paid">Đã thanh toán</SelectItem>
                            <SelectItem value="pending">Chưa thanh toán</SelectItem>
                            <SelectItem value="failed">Thanh toán thất bại</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="bg-gray-700/50 p-3 rounded">
                        <p className="text-gray-400 text-sm">
                          <strong>Mã đơn hàng:</strong> {orderDetailsData.order.order_number}
                        </p>
                        <p className="text-gray-400 text-sm">
                          <strong>Ngày tạo:</strong> {formatDate(orderDetailsData.order.created_at)}
                        </p>
                        <p className="text-gray-400 text-sm">
                          <strong>Tổng tiền:</strong> {formatPrice(orderDetailsData.order.total_amount)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-2 flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-400" />
                      Thông tin khách hàng
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Tên khách hàng:</span>
                        <span className="text-white">{orderDetailsData.order.shipping_recipient_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Email:</span>
                        <span className="text-white">{orderDetailsData.order.user_id.email}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h3 className="text-white font-medium mb-2 flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                      Địa chỉ giao hàng
                    </h3>
                    <p className="text-sm text-white bg-gray-700 p-2 rounded">
                      {orderDetailsData.order.shipping_address}
                    </p>
                    {orderDetailsData.order.notes && (
                      <div className="mt-2 pt-2 border-t border-gray-600">
                        <p className="text-gray-400 text-xs">Ghi chú:</p>
                        <p className="text-sm text-white">{orderDetailsData.order.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Chi tiết sản phẩm */}
                <div className="bg-gray-700/50 p-4 rounded-lg">
                  <h3 className="text-white font-medium mb-4 flex items-center">
                    <Package className="h-4 w-4 mr-2 text-gray-400" />
                    Chi tiết sản phẩm
                  </h3>
                  <div className="space-y-3">
                    {orderDetailsData.orderItems?.map((item: any, index: number) => (
                      <div key={index} className="flex justify-between border-b border-gray-600 pb-2 last:border-0">
                        <div className="flex-1">
                          <div className="text-white">{item.product_name}</div>
                          <div className="text-sm text-gray-400">
                            Đơn giá: {formatPrice(item.unit_price)} × {item.quantity}
                          </div>
                        </div>
                        <div className="text-right text-orange-500 font-medium">
                          {formatPrice(item.total_price)}
                        </div>
                      </div>
                    ))}

                    <div className="pt-2 mt-2 border-t border-gray-600">
                      <div className="flex justify-between text-gray-400">
                        <span>Tạm tính:</span>
                        <span>{formatPrice(orderDetailsData.order.subtotal)}</span>
                      </div>
                      <div className="flex justify-between text-gray-400">
                        <span>Phí vận chuyển:</span>
                        <span>{formatPrice(orderDetailsData.order.shipping_fee)}</span>
                      </div>
                      <div className="flex justify-between font-medium text-white mt-2">
                        <span>Tổng tiền:</span>
                        <span className="text-orange-500">{formatPrice(orderDetailsData.order.total_amount)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-between">
                <Button 
                  variant="outline" 
                  className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Hủy
                </Button>
                <Button
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={handleSaveOrder}
                  disabled={updateOrderStatusMutation.isPending}
                >
                  {updateOrderStatusMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Đang lưu...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Lưu thay đổi
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
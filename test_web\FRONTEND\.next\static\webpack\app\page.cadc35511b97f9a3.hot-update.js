"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAPI: () => (/* binding */ adminAPI),\n/* harmony export */   adminOrderAPI: () => (/* binding */ adminOrderAPI),\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   cartAPI: () => (/* binding */ cartAPI),\n/* harmony export */   orderAPI: () => (/* binding */ orderAPI),\n/* harmony export */   publicAPI: () => (/* binding */ publicAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Cấu hình axios instance\nconst API_URL = 'http://localhost:5000/api';\n// Tạo axios instance mặc định\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    withCredentials: true\n});\n// Xử lý gửi token trong request\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('accessToken');\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Xử lý refresh token khi token hết hạn\napi.interceptors.response.use((response)=>response, async (error)=>{\n    var _error_response;\n    const originalRequest = error.config;\n    // Nếu lỗi 401 (Unauthorized) và chưa thử refresh token\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            // Gọi API refresh token\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(API_URL, \"/auth/refresh-token\"), {}, {\n                withCredentials: true\n            });\n            // Lưu token mới\n            const { accessToken } = response.data;\n            localStorage.setItem('accessToken', accessToken);\n            // Cập nhật token trong header và thử lại request\n            originalRequest.headers.Authorization = \"Bearer \".concat(accessToken);\n            return api(originalRequest);\n        } catch (error) {\n            // Nếu refresh token thất bại, đăng xuất người dùng\n            localStorage.removeItem('accessToken');\n            localStorage.removeItem('userData');\n            window.location.href = '/auth';\n            return Promise.reject(error);\n        }\n    }\n    return Promise.reject(error);\n});\n// Auth API\nconst authAPI = {\n    login: async (email, password)=>{\n        const response = await api.post('/auth/login', {\n            email,\n            password\n        });\n        return response.data;\n    },\n    logout: async ()=>{\n        const response = await api.post('/auth/logout');\n        return response.data;\n    },\n    refreshToken: async ()=>{\n        const response = await api.post('/auth/refresh-token');\n        return response.data;\n    }\n};\n// User API\nconst userAPI = {\n    register: async (userData)=>{\n        const response = await api.post('/users/register', userData);\n        return response.data;\n    },\n    getCurrentUser: async ()=>{\n        const response = await api.get('/users/profile');\n        return response.data;\n    },\n    updateProfile: async (userData)=>{\n        const response = await api.put('/users/profile', userData);\n        return response.data;\n    },\n    changePassword: async (passwordData)=>{\n        const response = await api.put('/users/change-password', passwordData);\n        return response.data;\n    }\n};\n// Admin API\nconst adminAPI = {\n    // Quản lý người dùng\n    getAllUsers: async ()=>{\n        const response = await api.get('/users');\n        return response.data;\n    },\n    updateUserStatus: async (userId, isActive)=>{\n        const response = await api.put(\"/users/\".concat(userId, \"/status\"), {\n            is_active: isActive\n        });\n        return response.data;\n    },\n    createUser: async (userData)=>{\n        const response = await api.post('/users/register', userData);\n        return response.data;\n    },\n    updateUser: async (userId, userData)=>{\n        const response = await api.put(\"/users/\".concat(userId), userData);\n        return response.data;\n    },\n    deleteUser: async (userId)=>{\n        const response = await api.delete(\"/users/\".concat(userId));\n        return response.data;\n    },\n    // Quản lý danh mục\n    getAllCategories: async ()=>{\n        const response = await api.get('/categories/admin/all');\n        return response.data;\n    },\n    getCategoryById: async (categoryId)=>{\n        const response = await api.get(\"/categories/\".concat(categoryId));\n        return response.data;\n    },\n    createCategory: async (categoryData)=>{\n        const response = await api.post('/categories', categoryData);\n        return response.data;\n    },\n    updateCategory: async (categoryId, categoryData)=>{\n        const response = await api.put(\"/categories/\".concat(categoryId), categoryData);\n        return response.data;\n    },\n    updateCategoryStatus: async (categoryId, isActive)=>{\n        const response = await api.put(\"/categories/\".concat(categoryId, \"/status\"), {\n            is_active: isActive\n        });\n        return response.data;\n    },\n    // Quản lý sản phẩm\n    getAllProducts: async (params)=>{\n        const response = await api.get('/products', {\n            params\n        });\n        return response.data;\n    },\n    getProductById: async (productId)=>{\n        const response = await api.get(\"/products/\".concat(productId));\n        return response.data;\n    },\n    createProduct: async (productData)=>{\n        const response = await api.post('/products', productData);\n        return response.data;\n    },\n    updateProduct: async (productId, productData)=>{\n        const response = await api.put(\"/products/\".concat(productId), productData);\n        return response.data;\n    },\n    updateProductStatus: async (productId, isActive)=>{\n        const response = await api.put(\"/products/\".concat(productId, \"/status\"), {\n            is_active: isActive\n        });\n        return response.data;\n    },\n    updateProductStock: async (productId, stockQuantity)=>{\n        const response = await api.put(\"/products/\".concat(productId, \"/stock\"), {\n            stock_quantity: stockQuantity\n        });\n        return response.data;\n    },\n    deleteProduct: async (productId)=>{\n        const response = await api.delete(\"/products/\".concat(productId));\n        return response.data;\n    }\n};\n// Public API (không cần authentication)\nconst publicAPI = {\n    // Lấy danh sách sản phẩm công khai\n    getProducts: async (params)=>{\n        const response = await api.get('/products', {\n            params\n        });\n        return response.data;\n    },\n    // Lấy sản phẩm theo ID\n    getProductById: async (productId)=>{\n        const response = await api.get(\"/products/\".concat(productId));\n        return response.data;\n    },\n    // Lấy danh sách categories công khai\n    getCategories: async ()=>{\n        const response = await api.get('/categories');\n        return response.data;\n    },\n    // Lấy category theo ID\n    getCategoryById: async (categoryId)=>{\n        const response = await api.get(\"/categories/\".concat(categoryId));\n        return response.data;\n    }\n};\n// Cart API\nconst cartAPI = {\n    // Lấy giỏ hàng của người dùng\n    getCart: async ()=>{\n        const response = await api.get('/cart');\n        return response.data;\n    },\n    // Thêm sản phẩm vào giỏ hàng\n    addToCart: async (data)=>{\n        const response = await api.post('/cart', data);\n        return response.data;\n    },\n    // Cập nhật số lượng sản phẩm trong giỏ hàng\n    updateCartItem: async (cartItemId, data)=>{\n        const response = await api.put(\"/cart/\".concat(cartItemId), data);\n        return response.data;\n    },\n    // Xóa sản phẩm khỏi giỏ hàng\n    removeFromCart: async (cartItemId)=>{\n        const response = await api.delete(\"/cart/\".concat(cartItemId));\n        return response.data;\n    },\n    // Xóa toàn bộ giỏ hàng\n    clearCart: async ()=>{\n        const response = await api.delete('/cart');\n        return response.data;\n    }\n};\n// Order API\nconst orderAPI = {\n    // Tạo đơn hàng mới từ giỏ hàng\n    createOrder: async (data)=>{\n        const response = await api.post('/orders', data);\n        return response.data;\n    },\n    // Lấy danh sách đơn hàng của người dùng\n    getUserOrders: async ()=>{\n        const response = await api.get('/orders/my-orders');\n        return response.data;\n    },\n    // Lấy thông tin chi tiết đơn hàng\n    getOrderDetails: async (orderId)=>{\n        const response = await api.get(\"/orders/my-orders/\".concat(orderId));\n        return response.data;\n    },\n    // Hủy đơn hàng\n    cancelOrder: async (orderId)=>{\n        const response = await api.put(\"/orders/my-orders/\".concat(orderId, \"/cancel\"));\n        return response.data;\n    }\n};\n// Admin Order API\nconst adminOrderAPI = {\n    // Lấy danh sách tất cả đơn hàng (admin)\n    getAllOrders: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        if (filters.page) params.append('page', filters.page.toString());\n        if (filters.limit) params.append('limit', filters.limit.toString());\n        if (filters.status && filters.status !== 'all') params.append('status', filters.status);\n        const response = await api.get(\"/admin/orders?\".concat(params.toString()));\n        return response.data;\n    },\n    // Lấy chi tiết đơn hàng (admin)\n    getOrderDetails: async (orderId)=>{\n        const response = await api.get(\"/admin/orders/\".concat(orderId));\n        return response.data;\n    },\n    // Cập nhật trạng thái đơn hàng (admin)\n    updateOrderStatus: async (orderId, data)=>{\n        const response = await api.put(\"/admin/orders/\".concat(orderId, \"/status\"), data);\n        return response.data;\n    },\n    // Xóa đơn hàng (admin)\n    deleteOrder: async (orderId)=>{\n        const response = await api.delete(\"/admin/orders/\".concat(orderId));\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});
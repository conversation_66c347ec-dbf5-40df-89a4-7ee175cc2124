import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { cartAPI, type CartItem, type AddToCartData, type UpdateCartItemData } from '@/lib/api';
import { toast } from 'sonner';

// Hook để lấy giỏ hàng
export const useCart = () => {
  return useQuery({
    queryKey: ['cart'],
    queryFn: cartAPI.getCart,
    staleTime: 1 * 60 * 1000, // 1 phút
  });
};

// Hook để thêm sản phẩm vào giỏ hàng
export const useAddToCart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddToCartData) => cartAPI.addToCart(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      toast.success(data.message || '<PERSON><PERSON> thêm sản phẩm vào giỏ hàng!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Có lỗi xảy ra khi thêm vào giỏ hàng';
      toast.error(message);
    },
  });
};

// Hook để cập nhật số lượng sản phẩm trong giỏ hàng
export const useUpdateCartItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ cartItemId, data }: { cartItemId: string; data: UpdateCartItemData }) =>
      cartAPI.updateCartItem(cartItemId, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      toast.success(data.message || 'Đã cập nhật giỏ hàng!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật giỏ hàng';
      toast.error(message);
    },
  });
};

// Hook để xóa sản phẩm khỏi giỏ hàng
export const useRemoveFromCart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (cartItemId: string) => cartAPI.removeFromCart(cartItemId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      toast.success(data.message || 'Đã xóa sản phẩm khỏi giỏ hàng!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Có lỗi xảy ra khi xóa sản phẩm';
      toast.error(message);
    },
  });
};

// Hook để xóa toàn bộ giỏ hàng
export const useClearCart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => cartAPI.clearCart(),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
      toast.success(data.message || 'Đã xóa toàn bộ giỏ hàng!');
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Có lỗi xảy ra khi xóa giỏ hàng';
      toast.error(message);
    },
  });
};

// Hook để lấy số lượng sản phẩm trong giỏ hàng
export const useCartCount = () => {
  const { data: cartData } = useCart();
  return cartData?.totalItems || 0;
};

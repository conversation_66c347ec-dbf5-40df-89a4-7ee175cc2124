"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/orders/page",{

/***/ "(app-pages-browser)/./app/admin/orders/page.tsx":
/*!***********************************!*\
  !*** ./app/admin/orders/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./components/ui/pagination.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,Edit,Eye,FileText,MapPin,Package,RefreshCw,Save,Search,ShoppingBag,Truck,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-admin-orders */ \"(app-pages-browser)/./hooks/use-admin-orders.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction OrdersPage() {\n    var _selectedOrder, _selectedOrder1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [paymentFilter, setPaymentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isViewDialogOpen, setIsViewDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedOrderId, setSelectedOrderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [editedOrder, setEditedOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // API hooks\n    const { data: ordersData, isLoading: ordersLoading, error: ordersError, refetch: refetchOrders } = (0,_hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useAdminOrders)({\n        page: currentPage,\n        limit: 10,\n        status: statusFilter !== 'all' ? statusFilter : undefined\n    });\n    const { data: orderDetailsData, isLoading: detailsLoading } = (0,_hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useAdminOrderDetails)(selectedOrderId);\n    const updateOrderStatusMutation = (0,_hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useUpdateOrderStatus)();\n    const orderStats = (0,_hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useOrderStats)();\n    const orders = (ordersData === null || ordersData === void 0 ? void 0 : ordersData.orders) || [];\n    const pagination = ordersData === null || ordersData === void 0 ? void 0 : ordersData.pagination;\n    // Lọc đơn hàng theo search term và payment status\n    const filteredOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"OrdersPage.useMemo[filteredOrders]\": ()=>{\n            return orders.filter({\n                \"OrdersPage.useMemo[filteredOrders]\": (order)=>{\n                    const customerName = \"\".concat(order.user_id.first_name || '', \" \").concat(order.user_id.last_name || '').trim();\n                    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) || customerName.toLowerCase().includes(searchTerm.toLowerCase()) || order.user_id.email.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesPayment = paymentFilter === \"all\" || order.payment_status === paymentFilter;\n                    return matchesSearch && matchesPayment;\n                }\n            }[\"OrdersPage.useMemo[filteredOrders]\"]);\n        }\n    }[\"OrdersPage.useMemo[filteredOrders]\"], [\n        orders,\n        searchTerm,\n        paymentFilter\n    ]);\n    // Định dạng số tiền\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"vi-VN\", {\n            style: \"currency\",\n            currency: \"VND\"\n        }).format(price);\n    };\n    // Định dạng ngày tháng\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('vi-VN', {\n            year: 'numeric',\n            month: '2-digit',\n            day: '2-digit',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // Xử lý mở dialog xem chi tiết đơn hàng\n    const handleViewOrder = (orderId)=>{\n        setSelectedOrderId(orderId);\n        setIsViewDialogOpen(true);\n    };\n    // Xử lý mở dialog chỉnh sửa đơn hàng\n    const handleEditOrder = (order)=>{\n        setSelectedOrderId(order._id);\n        setEditedOrder({\n            order_status: order.order_status,\n            payment_status: order.payment_status\n        });\n        setIsEditDialogOpen(true);\n    };\n    // Xử lý lưu thay đổi đơn hàng\n    const handleSaveOrder = async ()=>{\n        if (!selectedOrderId) return;\n        try {\n            await updateOrderStatusMutation.mutateAsync({\n                orderId: selectedOrderId,\n                data: editedOrder\n            });\n            setIsEditDialogOpen(false);\n            setSelectedOrderId(\"\");\n            setEditedOrder({});\n        } catch (error) {\n        // Error được xử lý trong hook\n        }\n    };\n    // Xử lý thay đổi trạng thái đơn hàng\n    const handleStatusChange = (value)=>{\n        setEditedOrder((prev)=>({\n                ...prev,\n                order_status: value\n            }));\n    };\n    // Xử lý thay đổi trạng thái thanh toán\n    const handlePaymentStatusChange = (value)=>{\n        setEditedOrder((prev)=>({\n                ...prev,\n                payment_status: value\n            }));\n    };\n    // Xử lý refresh data\n    const handleRefresh = ()=>{\n        refetchOrders();\n        sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success('Đã làm mới dữ liệu');\n    };\n    // Xác định màu sắc cho badge trạng thái đơn hàng\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"delivered\":\n                return \"bg-green-500 hover:bg-green-600\";\n            case \"shipping\":\n                return \"bg-blue-500 hover:bg-blue-600\";\n            case \"pending\":\n                return \"bg-yellow-500 hover:bg-yellow-600\";\n            case \"confirmed\":\n                return \"bg-purple-500 hover:bg-purple-600\";\n            case \"cancelled\":\n                return \"bg-red-500 hover:bg-red-600\";\n            default:\n                return \"bg-gray-500 hover:bg-gray-600\";\n        }\n    };\n    // Xác định màu sắc cho badge trạng thái thanh toán\n    const getPaymentStatusColor = (status)=>{\n        switch(status){\n            case \"paid\":\n                return \"bg-green-500 hover:bg-green-600\";\n            case \"pending\":\n                return \"bg-yellow-500 hover:bg-yellow-600\";\n            case \"failed\":\n                return \"bg-red-500 hover:bg-red-600\";\n            default:\n                return \"bg-gray-500 hover:bg-gray-600\";\n        }\n    };\n    // Xác định icon cho trạng thái đơn hàng\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"delivered\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 16\n                }, this);\n            case \"shipping\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 16\n                }, this);\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"h-5 w-5 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"h-5 w-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Chuyển đổi status sang tiếng Việt\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"Đang xử lý\";\n            case \"confirmed\":\n                return \"Đã xác nhận\";\n            case \"shipping\":\n                return \"Đang giao hàng\";\n            case \"delivered\":\n                return \"Đã giao hàng\";\n            case \"cancelled\":\n                return \"Đã hủy\";\n            default:\n                return status;\n        }\n    };\n    const getPaymentStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"Chưa thanh toán\";\n            case \"paid\":\n                return \"Đã thanh toán\";\n            case \"failed\":\n                return \"Thanh toán thất bại\";\n            default:\n                return status;\n        }\n    };\n    // Loading state\n    if (ordersLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-6 w-6 mr-2 text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Quản l\\xfd đơn h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gray-800 border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 bg-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 bg-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-10 bg-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gray-800 border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 p-4\",\n                                    children: Array.from({\n                                        length: 5\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                            className: \"h-16 bg-gray-700\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (ordersError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-6 w-6 mr-2 text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Quản l\\xfd đơn h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"bg-gray-800 border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white mb-2\",\n                                children: \"C\\xf3 lỗi xảy ra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"Kh\\xf4ng thể tải danh s\\xe1ch đơn h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleRefresh,\n                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Thử lại\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-6 w-6 mr-2 text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Quản l\\xfd đơn h\\xe0ng\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleRefresh,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                \"L\\xe0m mới\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Tổng đơn h\\xe0ng\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: orderStats.total\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Đang xử l\\xfd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-yellow-500\",\n                                                children: orderStats.pending\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Đ\\xe3 giao h\\xe0ng\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-500\",\n                                        children: orderStats.delivered\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"bg-gray-800 border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Doanh thu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-500\",\n                                        children: formatPrice(orderStats.totalRevenue)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800 border-gray-700 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"text-white text-lg\",\n                            children: \"Bộ lọc\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"T\\xecm theo m\\xe3 đơn, t\\xean kh\\xe1ch h\\xe0ng...\",\n                                            className: \"bg-gray-700 border-gray-600 text-white pl-10\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                    value: statusFilter,\n                                    onValueChange: setStatusFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                            className: \"bg-gray-700 border-gray-600 text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                placeholder: \"Trạng th\\xe1i đơn h\\xe0ng\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                            className: \"bg-gray-700 border-gray-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"Tất cả trạng th\\xe1i\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"pending\",\n                                                    children: \"Đang xử l\\xfd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"confirmed\",\n                                                    children: \"Đ\\xe3 x\\xe1c nhận\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"shipping\",\n                                                    children: \"Đang giao h\\xe0ng\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"delivered\",\n                                                    children: \"Đ\\xe3 giao h\\xe0ng\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"cancelled\",\n                                                    children: \"Đ\\xe3 hủy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                    value: paymentFilter,\n                                    onValueChange: setPaymentFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                            className: \"bg-gray-700 border-gray-600 text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                placeholder: \"Trạng th\\xe1i thanh to\\xe1n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                            className: \"bg-gray-700 border-gray-600 text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"Tất cả trạng th\\xe1i\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"paid\",\n                                                    children: \"Đ\\xe3 thanh to\\xe1n\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"pending\",\n                                                    children: \"Chưa thanh to\\xe1n\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                    value: \"failed\",\n                                                    children: \"Thanh to\\xe1n thất bại\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"bg-gray-800 border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                className: \"bg-gray-900\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                    className: \"border-gray-700 hover:bg-gray-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"M\\xe3 ĐH\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"Kh\\xe1ch h\\xe0ng\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"Ng\\xe0y đặt\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"Tổng tiền\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"Trạng th\\xe1i\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400\",\n                                            children: \"Thanh to\\xe1n\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                            className: \"text-gray-400 text-right\",\n                                            children: \"Thao t\\xe1c\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                children: [\n                                    filteredOrders.map((order)=>{\n                                        const customerName = \"\".concat(order.user_id.first_name || '', \" \").concat(order.user_id.last_name || '').trim() || 'N/A';\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"border-gray-700 hover:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium text-white\",\n                                                    children: order.order_number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: customerName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: order.user_id.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"text-gray-300\",\n                                                    children: formatDate(order.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"text-orange-500 font-medium\",\n                                                    children: formatPrice(order.total_amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: getStatusColor(order.order_status),\n                                                        children: getStatusText(order.order_status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: getPaymentStatusColor(order.payment_status),\n                                                        children: getPaymentStatusText(order.payment_status)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"h-8 border-gray-600 text-gray-300 hover:text-white hover:bg-gray-600\",\n                                                                onClick: ()=>handleViewOrder(order._id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"h-8 border-gray-600 text-blue-400 hover:text-white hover:bg-blue-900 hover:border-blue-700\",\n                                                                onClick: ()=>handleEditOrder(order),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, order._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this);\n                                    }),\n                                    filteredOrders.length === 0 && !ordersLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        className: \"border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                            colSpan: 7,\n                                            className: \"h-24 text-center text-gray-400\",\n                                            children: searchTerm || paymentFilter !== 'all' ? 'Không tìm thấy đơn hàng nào phù hợp' : 'Chưa có đơn hàng nào'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-between items-center text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \"Hiển thị 1-6 trong tổng số \",\n                            filteredOrders.length,\n                            \" đơn h\\xe0ng\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.Pagination, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationPrevious, {\n                                        href: \"#\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationLink, {\n                                        href: \"#\",\n                                        isActive: true,\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationLink, {\n                                        href: \"#\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationEllipsis, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_11__.PaginationNext, {\n                                        href: \"#\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isViewDialogOpen,\n                onOpenChange: setIsViewDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"bg-gray-800 text-white border-gray-700 sm:max-w-[700px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-xl text-white flex items-center\",\n                                    children: [\n                                        selectedOrder && getStatusIcon(selectedOrder.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: [\n                                                \"Chi tiết đơn h\\xe0ng \",\n                                                (_selectedOrder = selectedOrder) === null || _selectedOrder === void 0 ? void 0 : _selectedOrder.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    className: \"text-gray-400\",\n                                    children: \"Th\\xf4ng tin chi tiết về đơn h\\xe0ng v\\xe0 trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this),\n                        selectedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-2 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Th\\xf4ng tin đơn h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"M\\xe3 đơn h\\xe0ng:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: selectedOrder.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Ng\\xe0y đặt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: selectedOrder.date\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Trạng th\\xe1i:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: getStatusColor(selectedOrder.status),\n                                                                            children: selectedOrder.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Phương thức thanh to\\xe1n:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: selectedOrder.paymentMethod\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Trạng th\\xe1i thanh to\\xe1n:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: getPaymentStatusColor(selectedOrder.paymentStatus),\n                                                                            children: selectedOrder.paymentStatus\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                selectedOrder.trackingNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"M\\xe3 vận đơn:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: selectedOrder.trackingNumber\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-2 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Th\\xf4ng tin kh\\xe1ch h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"T\\xean kh\\xe1ch h\\xe0ng:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: selectedOrder.customerName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Email:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: selectedOrder.customerEmail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-2 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Địa chỉ giao h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-white\",\n                                                            children: selectedOrder.shippingAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Chi tiết sản phẩm\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        selectedOrder.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between border-b border-gray-600 pb-2 last:border-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white\",\n                                                                                children: item.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 617,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-400\",\n                                                                                children: [\n                                                                                    \"SL: \",\n                                                                                    item.quantity\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right text-orange-500 font-medium\",\n                                                                        children: formatPrice(item.price * item.quantity)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-2 mt-2 border-t border-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Tổng sản phẩm:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                selectedOrder.items.reduce((sum, item)=>sum + item.quantity, 0),\n                                                                                \" sản phẩm\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between font-medium text-white mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Tổng tiền:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-orange-500\",\n                                                                            children: formatPrice(selectedOrder.total)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 633,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"In h\\xf3a đơn\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>setIsViewDialogOpen(false),\n                                            className: \"bg-orange-600 hover:bg-orange-700\",\n                                            children: \"Đ\\xf3ng\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isEditDialogOpen,\n                onOpenChange: setIsEditDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"bg-gray-800 text-white border-gray-700 sm:max-w-[700px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-xl text-white flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Chỉnh sửa đơn h\\xe0ng \",\n                                                (_selectedOrder1 = selectedOrder) === null || _selectedOrder1 === void 0 ? void 0 : _selectedOrder1.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    className: \"text-gray-400\",\n                                    children: \"Cập nhật th\\xf4ng tin đơn h\\xe0ng v\\xe0 trạng th\\xe1i\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this),\n                        editedOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-3 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Th\\xf4ng tin đơn h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                            htmlFor: \"orderStatus\",\n                                                                            className: \"text-gray-300 mb-1 block\",\n                                                                            children: \"Trạng th\\xe1i đơn h\\xe0ng\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 687,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                            value: editedOrder.status,\n                                                                            onValueChange: handleStatusChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                    className: \"bg-gray-700 border-gray-600 text-white\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                        placeholder: \"Chọn trạng th\\xe1i\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 692,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                    className: \"bg-gray-700 border-gray-600 text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đang xử l\\xfd\",\n                                                                                            children: \"Đang xử l\\xfd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 695,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đ\\xe3 x\\xe1c nhận\",\n                                                                                            children: \"Đ\\xe3 x\\xe1c nhận\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 696,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đang giao h\\xe0ng\",\n                                                                                            children: \"Đang giao h\\xe0ng\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đ\\xe3 giao h\\xe0ng\",\n                                                                                            children: \"Đ\\xe3 giao h\\xe0ng\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đ\\xe3 hủy\",\n                                                                                            children: \"Đ\\xe3 hủy\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 699,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 694,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                            htmlFor: \"paymentStatus\",\n                                                                            className: \"text-gray-300 mb-1 block\",\n                                                                            children: \"Trạng th\\xe1i thanh to\\xe1n\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                            value: editedOrder.paymentStatus,\n                                                                            onValueChange: handlePaymentStatusChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                    className: \"bg-gray-700 border-gray-600 text-white\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                        placeholder: \"Chọn trạng th\\xe1i\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 710,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                    className: \"bg-gray-700 border-gray-600 text-white\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Đ\\xe3 thanh to\\xe1n\",\n                                                                                            children: \"Đ\\xe3 thanh to\\xe1n\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 713,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Chưa thanh to\\xe1n\",\n                                                                                            children: \"Chưa thanh to\\xe1n\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 714,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                            value: \"Ho\\xe0n tiền\",\n                                                                                            children: \"Ho\\xe0n tiền\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                    lineNumber: 712,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                            htmlFor: \"trackingNumber\",\n                                                                            className: \"text-gray-300 mb-1 block\",\n                                                                            children: \"M\\xe3 vận đơn\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            id: \"trackingNumber\",\n                                                                            value: editedOrder.trackingNumber || \"\",\n                                                                            onChange: (e)=>setEditedOrder({\n                                                                                    ...editedOrder,\n                                                                                    trackingNumber: e.target.value\n                                                                                }),\n                                                                            className: \"bg-gray-700 border-gray-600 text-white\",\n                                                                            placeholder: \"Nhập m\\xe3 vận đơn\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-2 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Th\\xf4ng tin kh\\xe1ch h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"T\\xean kh\\xe1ch h\\xe0ng:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: editedOrder.customerName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Email:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: editedOrder.customerEmail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-medium mb-2 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 754,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Địa chỉ giao h\\xe0ng\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: editedOrder.shippingAddress,\n                                                            onChange: (e)=>setEditedOrder({\n                                                                    ...editedOrder,\n                                                                    shippingAddress: e.target.value\n                                                                }),\n                                                            className: \"w-full rounded-md bg-gray-700 border-gray-600 text-white p-2 text-sm\",\n                                                            rows: 2\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-700/50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Chi tiết sản phẩm\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        editedOrder.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between border-b border-gray-600 pb-2 last:border-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white\",\n                                                                                children: item.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-400\",\n                                                                                children: [\n                                                                                    \"Đơn gi\\xe1: \",\n                                                                                    formatPrice(item.price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 777,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                className: \"text-gray-400 mr-2\",\n                                                                                children: \"SL:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 780,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                type: \"number\",\n                                                                                min: \"1\",\n                                                                                value: item.quantity,\n                                                                                onChange: (e)=>handleQuantityChange(index, parseInt(e.target.value) || 1),\n                                                                                className: \"w-16 h-8 bg-gray-700 border-gray-600 text-white text-center\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 781,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right text-orange-500 font-medium ml-4 w-28\",\n                                                                        children: formatPrice(item.price * item.quantity)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-2 mt-2 border-t border-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Tổng sản phẩm:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                editedOrder.items.reduce((sum, item)=>sum + item.quantity, 0),\n                                                                                \" sản phẩm\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between font-medium text-white mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Tổng tiền:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-orange-500\",\n                                                                            children: formatPrice(editedOrder.total)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            className: \"border-gray-600 text-gray-300 hover:text-white hover:bg-gray-700\",\n                                            onClick: ()=>setIsEditDialogOpen(false),\n                                            children: \"Hủy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: handleSaveOrder,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_Edit_Eye_FileText_MapPin_Package_RefreshCw_Save_Search_ShoppingBag_Truck_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Lưu thay đổi\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n                lineNumber: 663,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test_web\\\\test_web\\\\FRONTEND\\\\app\\\\admin\\\\orders\\\\page.tsx\",\n        lineNumber: 317,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersPage, \"5AHDmum2Px7iNtpM4ANrzxUdLpY=\", false, function() {\n    return [\n        _hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useAdminOrders,\n        _hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useAdminOrderDetails,\n        _hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useUpdateOrderStatus,\n        _hooks_use_admin_orders__WEBPACK_IMPORTED_MODULE_12__.useOrderStats\n    ];\n});\n_c = OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/orders/page.tsx\n"));

/***/ })

});